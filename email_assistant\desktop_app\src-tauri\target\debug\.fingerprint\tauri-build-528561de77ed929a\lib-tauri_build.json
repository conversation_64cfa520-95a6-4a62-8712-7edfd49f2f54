{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 14484331888355962222, "deps": [[2671782512663819132, "tauri_utils", false, 11350530624544277308], [4899080583175475170, "semver", false, 16636169827845809847], [6913375703034175521, "schemars", false, 2026813950441894349], [7170110829644101142, "json_patch", false, 5451797175141292056], [9689903380558560274, "serde", false, 14955956804811053645], [12714016054753183456, "tauri_winres", false, 16400311295406707139], [13077543566650298139, "heck", false, 12227043807642275620], [13475171727366188400, "cargo_toml", false, 1847275727613539780], [13625485746686963219, "anyhow", false, 16521415306001099308], [15367738274754116744, "serde_json", false, 9951868567464697481], [15609422047640926750, "toml", false, 4825197427743863217], [15622660310229662834, "walkdir", false, 4450029676203649764], [16928111194414003569, "dirs", false, 477640866065860759], [17155886227862585100, "glob", false, 16666287207728800018]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-528561de77ed929a\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}