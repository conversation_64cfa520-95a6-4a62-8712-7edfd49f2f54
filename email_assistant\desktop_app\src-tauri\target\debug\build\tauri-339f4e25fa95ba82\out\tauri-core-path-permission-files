["\\\\?\\C:\\Users\\<USER>\\Desktop\\thunder\\email_assistant\\desktop_app\\src-tauri\\target\\debug\\build\\tauri-339f4e25fa95ba82\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\C:\\Users\\<USER>\\Desktop\\thunder\\email_assistant\\desktop_app\\src-tauri\\target\\debug\\build\\tauri-339f4e25fa95ba82\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\C:\\Users\\<USER>\\Desktop\\thunder\\email_assistant\\desktop_app\\src-tauri\\target\\debug\\build\\tauri-339f4e25fa95ba82\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\C:\\Users\\<USER>\\Desktop\\thunder\\email_assistant\\desktop_app\\src-tauri\\target\\debug\\build\\tauri-339f4e25fa95ba82\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\C:\\Users\\<USER>\\Desktop\\thunder\\email_assistant\\desktop_app\\src-tauri\\target\\debug\\build\\tauri-339f4e25fa95ba82\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\C:\\Users\\<USER>\\Desktop\\thunder\\email_assistant\\desktop_app\\src-tauri\\target\\debug\\build\\tauri-339f4e25fa95ba82\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\C:\\Users\\<USER>\\Desktop\\thunder\\email_assistant\\desktop_app\\src-tauri\\target\\debug\\build\\tauri-339f4e25fa95ba82\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\C:\\Users\\<USER>\\Desktop\\thunder\\email_assistant\\desktop_app\\src-tauri\\target\\debug\\build\\tauri-339f4e25fa95ba82\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\C:\\Users\\<USER>\\Desktop\\thunder\\email_assistant\\desktop_app\\src-tauri\\target\\debug\\build\\tauri-339f4e25fa95ba82\\out\\permissions\\path\\autogenerated\\default.toml"]