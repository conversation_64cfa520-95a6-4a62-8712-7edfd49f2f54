{"name": "@tauri-apps/cli-win32-x64-msvc", "version": "2.6.2", "repository": {"type": "git", "url": "git+https://github.com/tauri-apps/tauri.git"}, "homepage": "https://github.com/tauri-apps/tauri#readme", "bugs": {"url": "https://github.com/tauri-apps/tauri/issues"}, "contributors": ["Tauri Programme within The Commons Conservancy"], "license": "Apache-2.0 OR MIT", "publishConfig": {"access": "public"}, "os": ["win32"], "cpu": ["x64"], "main": "cli.win32-x64-msvc.node", "files": ["cli.win32-x64-msvc.node"], "engines": {"node": ">= 10"}}