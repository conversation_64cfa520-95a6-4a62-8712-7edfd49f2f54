# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-inner-position"
description = "Enables the inner_position command without any pre-configured scope."
commands.allow = ["inner_position"]

[[permission]]
identifier = "deny-inner-position"
description = "Denies the inner_position command without any pre-configured scope."
commands.deny = ["inner_position"]
