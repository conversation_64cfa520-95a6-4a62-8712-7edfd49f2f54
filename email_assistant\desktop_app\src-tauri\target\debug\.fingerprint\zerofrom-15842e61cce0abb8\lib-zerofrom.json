{"rustc": 1842507548689473721, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 15657897354478470176, "path": 5661795991871761775, "deps": [[4022439902832367970, "zerofrom_derive", false, 10325892742657223478]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerofrom-15842e61cce0abb8\\dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}