{"name": "@tauri-apps/cli", "version": "2.6.2", "description": "Command line interface for building Tauri apps", "funding": {"type": "opencollective", "url": "https://opencollective.com/tauri"}, "repository": {"type": "git", "url": "git+https://github.com/tauri-apps/tauri.git"}, "contributors": ["Tauri Programme within The Commons Conservancy"], "license": "Apache-2.0 OR MIT", "bugs": {"url": "https://github.com/tauri-apps/tauri/issues"}, "homepage": "https://github.com/tauri-apps/tauri#readme", "publishConfig": {"access": "public"}, "main": "main.js", "types": "main.d.ts", "napi": {"name": "cli", "triples": {"additional": ["aarch64-apple-darwin", "aarch64-unknown-linux-gnu", "aarch64-unknown-linux-musl", "armv7-unknown-linux-gnueabihf", "x86_64-unknown-linux-musl", "riscv64gc-unknown-linux-gnu", "i686-pc-windows-msvc", "aarch64-pc-windows-msvc"]}}, "devDependencies": {"@napi-rs/cli": "2.18.4", "@types/node": "^22.15.32", "cross-env": "7.0.3", "vitest": "^3.2.4"}, "engines": {"node": ">= 10"}, "bin": {"tauri": "./tauri.js"}, "scripts": {"artifacts": "napi artifacts", "build": "cross-env TARGET=node napi build --platform --profile release-size-optimized", "postbuild": "node append-headers.js", "build:debug": "cross-env TARGET=node napi build --platform", "postbuild:debug": "node append-headers.js", "prepublishOnly": "napi prepublish -t npm --gh-release-id $RELEASE_ID", "prepack": "cp ../../crates/tauri-schema-generator/schemas/config.schema.json .", "version": "napi version", "test": "vitest run", "tauri": "node ./tauri.js"}, "optionalDependencies": {"@tauri-apps/cli-win32-x64-msvc": "2.6.2", "@tauri-apps/cli-darwin-x64": "2.6.2", "@tauri-apps/cli-linux-x64-gnu": "2.6.2", "@tauri-apps/cli-darwin-arm64": "2.6.2", "@tauri-apps/cli-linux-arm64-gnu": "2.6.2", "@tauri-apps/cli-linux-arm64-musl": "2.6.2", "@tauri-apps/cli-linux-arm-gnueabihf": "2.6.2", "@tauri-apps/cli-linux-x64-musl": "2.6.2", "@tauri-apps/cli-linux-riscv64-gnu": "2.6.2", "@tauri-apps/cli-win32-ia32-msvc": "2.6.2", "@tauri-apps/cli-win32-arm64-msvc": "2.6.2"}}