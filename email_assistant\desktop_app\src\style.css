/* Modern CSS Variables for AI Email Assistant */
:root {
  /* Typography */
  --font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;

  /* Colors - Modern Professional Palette */
  --color-primary: #2563eb;
  --color-primary-hover: #1d4ed8;
  --color-primary-light: #dbeafe;
  --color-secondary: #64748b;
  --color-success: #059669;
  --color-warning: #d97706;
  --color-error: #dc2626;

  /* Neutral Colors */
  --color-white: #ffffff;
  --color-gray-50: #f8fafc;
  --color-gray-100: #f1f5f9;
  --color-gray-200: #e2e8f0;
  --color-gray-300: #cbd5e1;
  --color-gray-400: #94a3b8;
  --color-gray-500: #64748b;
  --color-gray-600: #475569;
  --color-gray-700: #334155;
  --color-gray-800: #1e293b;
  --color-gray-900: #0f172a;

  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  color: var(--color-gray-900);
  background-color: var(--color-gray-50);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* App Container */
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Loading Screen */
.loading-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-hover) 100%);
  color: var(--color-white);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid var(--color-white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Main Application Layout */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--color-gray-50);
}

.app-header {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-hover) 100%);
  color: var(--color-white);
  padding: var(--spacing-6) var(--spacing-8);
  box-shadow: var(--shadow-md);
}

.app-title {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.app-subtitle {
  font-size: var(--font-size-base);
  opacity: 0.9;
  margin-top: var(--spacing-2);
  font-weight: 400;
}

.app-main {
  flex: 1;
  display: flex;
  gap: var(--spacing-6);
  padding: var(--spacing-8);
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* Sidebar */
.sidebar {
  width: 320px;
  background: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-6);
  height: fit-content;
  position: sticky;
  top: var(--spacing-8);
}

/* Main Content Area */
.main-content {
  flex: 1;
  background: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-8);
  min-height: 600px;
}

/* Section Headers */
.section-header {
  margin-bottom: var(--spacing-6);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-2);
}

.section-description {
  color: var(--color-gray-600);
  font-size: var(--font-size-sm);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-5);
  font-size: var(--font-size-sm);
  font-weight: 500;
  font-family: var(--font-family);
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
  border: 1px solid var(--color-gray-300);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-gray-200);
  border-color: var(--color-gray-400);
}

.btn-success {
  background-color: var(--color-success);
  color: var(--color-white);
}

.btn-success:hover:not(:disabled) {
  background-color: #047857;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.status-healthy {
  background-color: #dcfce7;
  color: var(--color-success);
}

.status-error {
  background-color: #fef2f2;
  color: var(--color-error);
}

.status-warning {
  background-color: #fef3c7;
  color: var(--color-warning);
}

/* Status dot */
.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .app-main {
    flex-direction: column;
    padding: var(--spacing-4);
  }

  .sidebar {
    width: 100%;
    position: static;
  }
}

@media (max-width: 640px) {
  .app-header {
    padding: var(--spacing-4);
  }

  .app-title {
    font-size: var(--font-size-xl);
  }

  .main-content {
    padding: var(--spacing-4);
  }
}

/* Service Status Styles */
.service-status {
  margin-bottom: var(--spacing-6);
}

.status-item {
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  background-color: var(--color-gray-50);
  border: 1px solid var(--color-gray-200);
}

/* Quick Actions */
.quick-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

/* File Processing Styles */
.processing-status {
  padding: var(--spacing-6);
}

.processing-status h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-4);
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.file-item {
  padding: var(--spacing-4);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-md);
  background-color: var(--color-gray-50);
}

.file-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2);
}

.file-name {
  font-weight: 500;
  color: var(--color-gray-900);
}

.file-status {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: var(--color-gray-200);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--color-primary);
  transition: width var(--transition-normal);
  border-radius: var(--radius-sm);
}

/* Welcome Message */
.welcome-message {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.mb-4 {
  margin-bottom: var(--spacing-4);
}

.mt-4 {
  margin-top: var(--spacing-4);
}

/* Enhanced Drag and Drop Styles */
.drop-zone {
  border: 2px dashed var(--color-gray-300);
  border-radius: var(--radius-lg);
  padding: var(--spacing-8);
  text-align: center;
  transition: all var(--transition-fast);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.drop-zone:hover {
  border-color: var(--color-primary);
  background-color: var(--color-primary-light);
}

.main-content.drag-over .drop-zone,
.main-content.drag-over {
  border-color: var(--color-primary) !important;
  background-color: var(--color-primary-light) !important;
  transform: scale(1.01);
  box-shadow: var(--shadow-lg);
}

.main-content.drag-over::before {
  content: "Drop email files here to process";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--color-primary);
  color: var(--color-white);
  padding: var(--spacing-4) var(--spacing-6);
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: var(--font-size-lg);
  z-index: 1000;
  pointer-events: none;
  box-shadow: var(--shadow-xl);
}

/* Batch Processing Styles */
.batch-processing {
  padding: var(--spacing-6);
}

.file-processing-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.file-processing-item {
  background: var(--color-gray-50);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-5);
  transition: all var(--transition-fast);
}

.file-processing-item:hover {
  box-shadow: var(--shadow-md);
}

.file-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-4);
}

.file-info .file-name {
  font-weight: 600;
  color: var(--color-gray-900);
  font-size: var(--font-size-base);
  margin-bottom: var(--spacing-1);
}

.file-info .file-details {
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
}

.file-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.file-status.status-pending {
  color: var(--color-gray-600);
}

.file-status.status-processing {
  color: var(--color-primary);
}

.file-status.status-completed {
  color: var(--color-success);
}

.file-status.status-error {
  color: var(--color-error);
}

.progress-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-3);
}

.progress-bar {
  flex: 1;
  height: 8px;
  background-color: var(--color-gray-200);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--color-primary);
  transition: width var(--transition-normal);
  border-radius: var(--radius-sm);
}

.progress-text {
  font-size: var(--font-size-xs);
  color: var(--color-gray-600);
  font-weight: 500;
  min-width: 3rem;
  text-align: right;
}

.processing-result {
  margin-top: var(--spacing-3);
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
}

.result-success {
  background-color: #dcfce7;
  color: var(--color-success);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-sm);
}

.result-error {
  background-color: #fef2f2;
  color: var(--color-error);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.processing-actions {
  display: flex;
  gap: var(--spacing-3);
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--color-gray-200);
}

/* Validation Error Styles */
.validation-errors {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Button Variants */
.btn-sm {
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-xs);
}

.retry-btn {
  margin-left: auto;
}
