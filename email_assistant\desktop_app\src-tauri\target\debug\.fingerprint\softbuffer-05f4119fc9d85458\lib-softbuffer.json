{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 15657897354478470176, "path": 4872535670476380077, "deps": [[376837177317575824, "build_script_build", false, 18280112210821561278], [4143744114649553716, "raw_window_handle", false, 14178324387943116473], [5986029879202738730, "log", false, 17863289569455453767], [10281541584571964250, "windows_sys", false, 11679235071640375544]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-05f4119fc9d85458\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}