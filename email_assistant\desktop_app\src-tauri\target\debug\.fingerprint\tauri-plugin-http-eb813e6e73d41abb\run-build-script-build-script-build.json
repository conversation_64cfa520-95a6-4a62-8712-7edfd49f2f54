{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14039947826026167952, "build_script_build", false, 9844267916455972672], [6416823254013318197, "build_script_build", false, 9931762977411470621], [16171925541490437305, "build_script_build", false, 9136643135743246353]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri-plugin-http-eb813e6e73d41abb\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}