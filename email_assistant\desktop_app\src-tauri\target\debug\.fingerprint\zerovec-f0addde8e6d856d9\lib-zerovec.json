{"rustc": 1842507548689473721, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 15657897354478470176, "path": 7588903603151011990, "deps": [[9620753569207166497, "zerovec_derive", false, 16390555524858021372], [10706449961930108323, "yoke", false, 15462011844102136326], [17046516144589451410, "zerofrom", false, 227579120437212295]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerovec-f0addde8e6d856d9\\dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}