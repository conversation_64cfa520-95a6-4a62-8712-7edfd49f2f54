use serde::{Deserialize, Serialize};
use std::path::PathBuf;

#[derive(Debug, Serialize, Deserialize)]
pub struct ServiceHealthResponse {
    pub service: String,
    pub status: String,
    pub message: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EmailProcessingRequest {
    pub file_path: String,
    pub file_type: String, // "eml" or "mbox"
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EmailProcessingResponse {
    pub success: bool,
    pub message: String,
    pub processed_count: Option<u32>,
}

// Tauri command to check service health
#[tauri::command]
async fn check_service_health(service_url: String) -> Result<ServiceHealthResponse, String> {
    let client = reqwest::Client::new();

    match client.get(&format!("{}/health", service_url)).send().await {
        Ok(response) => {
            if response.status().is_success() {
                Ok(ServiceHealthResponse {
                    service: service_url,
                    status: "healthy".to_string(),
                    message: "Service is responding".to_string(),
                })
            } else {
                Ok(ServiceHealthResponse {
                    service: service_url,
                    status: "unhealthy".to_string(),
                    message: format!("Service returned status: {}", response.status()),
                })
            }
        }
        Err(e) => Ok(ServiceHealthResponse {
            service: service_url,
            status: "error".to_string(),
            message: format!("Failed to connect: {}", e),
        }),
    }
}

// Tauri command to process email files
#[tauri::command]
async fn process_email_file(request: EmailProcessingRequest) -> Result<EmailProcessingResponse, String> {
    let client = reqwest::Client::new();
    let ingestion_url = "http://localhost:8080"; // Default ingestion service URL

    // Send file path to ingestion service
    let response = client
        .post(&format!("{}/process", ingestion_url))
        .json(&request)
        .send()
        .await
        .map_err(|e| format!("Failed to send request: {}", e))?;

    if response.status().is_success() {
        let result: EmailProcessingResponse = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse response: {}", e))?;
        Ok(result)
    } else {
        Err(format!("Processing failed with status: {}", response.status()))
    }
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_http::init())
        .setup(|app| {
            if cfg!(debug_assertions) {
                app.handle().plugin(
                    tauri_plugin_log::Builder::default()
                        .level(log::LevelFilter::Info)
                        .build(),
                )?;
            }
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            check_service_health,
            process_email_file
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
