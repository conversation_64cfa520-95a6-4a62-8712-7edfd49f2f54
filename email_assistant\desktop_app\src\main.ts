import './style.css'
import { invoke } from '@tauri-apps/api/core'
import { open } from '@tauri-apps/plugin-dialog'
import { listen } from '@tauri-apps/api/event'

// Types for our application
interface ServiceHealthResponse {
  service: string;
  status: string;
  message: string;
}

interface EmailProcessingRequest {
  file_path: string;
  file_type: string;
}

interface EmailProcessingResponse {
  success: boolean;
  message: string;
  processed_count?: number;
}

interface FileMetadata {
  name: string;
  size: number;
  type: string;
  lastModified: number;
  path?: string;
}

interface ProcessingState {
  file: FileMetadata;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number;
  message: string;
  result?: EmailProcessingResponse;
}

// Application state
class EmailAssistantApp {
  private appElement: HTMLElement;
  private isInitialized = false;
  private processingQueue: ProcessingState[] = [];
  private maxFileSize = 100 * 1024 * 1024; // 100MB limit
  private allowedTypes = ['.eml', '.mbox'];

  constructor() {
    this.appElement = document.querySelector<HTMLDivElement>('#app')!;
    this.init();
  }

  private async init() {
    try {
      // Show loading screen initially
      this.showLoadingScreen();

      // Initialize the application
      await this.initializeApp();

      // Render the main interface
      this.renderMainInterface();

      // Set up event listeners
      this.setupEventListeners();

      // Check service health
      await this.checkServicesHealth();

      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize application:', error);
      this.showError('Failed to initialize application. Please check if all services are running.');
    }
  }

  private showLoadingScreen() {
    // Loading screen is already in HTML, just ensure it's visible
    const loadingScreen = this.appElement.querySelector('.loading-screen');
    if (loadingScreen) {
      (loadingScreen as HTMLElement).style.display = 'flex';
    }
  }

  private hideLoadingScreen() {
    const loadingScreen = this.appElement.querySelector('.loading-screen');
    if (loadingScreen) {
      (loadingScreen as HTMLElement).style.display = 'none';
    }
  }

  private async initializeApp() {
    // Simulate initialization delay
    await new Promise(resolve => setTimeout(resolve, 1500));
  }

  private renderMainInterface() {
    this.hideLoadingScreen();

    this.appElement.innerHTML = `
      <div class="app-container">
        <header class="app-header">
          <h1 class="app-title">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
              <polyline points="22,6 12,13 2,6"/>
            </svg>
            AI Email Assistant
          </h1>
          <p class="app-subtitle">Intelligent email processing and draft generation for legal professionals</p>
        </header>

        <main class="app-main">
          <aside class="sidebar">
            <div class="section-header">
              <h2 class="section-title">System Status</h2>
              <p class="section-description">Monitor service health and connectivity</p>
            </div>

            <div id="service-status" class="service-status">
              <div class="status-item">
                <span class="status-indicator status-warning">
                  <span class="status-dot"></span>
                  Checking services...
                </span>
              </div>
            </div>

            <div class="section-header" style="margin-top: 2rem;">
              <h2 class="section-title">Quick Actions</h2>
              <p class="section-description">Import and process email files</p>
            </div>

            <div class="quick-actions">
              <button id="select-files-btn" class="btn btn-primary" style="width: 100%; margin-bottom: 1rem;">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                  <polyline points="14,2 14,8 20,8"/>
                  <line x1="16" y1="13" x2="8" y2="13"/>
                  <line x1="16" y1="17" x2="8" y2="17"/>
                  <line x1="10" y1="9" x2="8" y2="9"/>
                </svg>
                Select Email Files
              </button>

              <button id="refresh-status-btn" class="btn btn-secondary" style="width: 100%;">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="23 4 23 10 17 10"/>
                  <polyline points="1 20 1 14 7 14"/>
                  <path d="m3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"/>
                </svg>
                Refresh Status
              </button>
            </div>
          </aside>

          <div class="main-content">
            <div class="section-header">
              <h2 class="section-title">Email Processing</h2>
              <p class="section-description">Import .eml or .mbox files to process emails and generate AI-powered draft responses</p>
            </div>

            <div id="main-content-area">
              <div class="welcome-message">
                <div class="drop-zone" style="text-align: center; padding: 3rem; color: var(--color-gray-500); border: 2px dashed var(--color-gray-300); border-radius: var(--radius-lg); transition: all var(--transition-fast);">
                  <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" style="margin-bottom: 1rem; opacity: 0.5;">
                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                    <polyline points="22,6 12,13 2,6"/>
                  </svg>
                  <h3 style="margin-bottom: 0.5rem; color: var(--color-gray-700);">Welcome to AI Email Assistant</h3>
                  <p style="margin-bottom: 1rem;">Select email files from the sidebar or drag and drop .eml/.mbox files here to begin processing.</p>
                  <div style="display: flex; align-items: center; justify-content: center; gap: 1rem; font-size: var(--font-size-sm); color: var(--color-gray-400);">
                    <span>📁 .eml files</span>
                    <span>•</span>
                    <span>📦 .mbox files</span>
                    <span>•</span>
                    <span>📏 Max ${this.formatFileSize(this.maxFileSize)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    `;
  }

  private setupEventListeners() {
    // Select files button
    const selectFilesBtn = document.getElementById('select-files-btn');
    if (selectFilesBtn) {
      selectFilesBtn.addEventListener('click', () => this.handleSelectFiles());
    }

    // Refresh status button
    const refreshStatusBtn = document.getElementById('refresh-status-btn');
    if (refreshStatusBtn) {
      refreshStatusBtn.addEventListener('click', () => this.checkServicesHealth());
    }

    // Drag and drop functionality
    this.setupDragAndDrop();
  }

  private setupDragAndDrop() {
    const mainContent = document.querySelector('.main-content');
    if (!mainContent) return;

    // Prevent default drag behaviors on the entire document
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      document.addEventListener(eventName, this.preventDefaults, false);
      mainContent.addEventListener(eventName, this.preventDefaults, false);
    });

    // Highlight drop area when item is dragged over it
    ['dragenter', 'dragover'].forEach(eventName => {
      mainContent.addEventListener(eventName, () => this.highlight(mainContent as HTMLElement), false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
      mainContent.addEventListener(eventName, () => this.unhighlight(mainContent as HTMLElement), false);
    });

    // Handle dropped files
    mainContent.addEventListener('drop', (e) => this.handleDrop(e), false);
  }

  private preventDefaults(e: Event) {
    e.preventDefault();
    e.stopPropagation();
  }

  private highlight(element: HTMLElement) {
    element.classList.add('drag-over');
  }

  private unhighlight(element: HTMLElement) {
    element.classList.remove('drag-over');
  }

  private async handleDrop(e: DragEvent) {
    const dt = e.dataTransfer;
    if (!dt) return;

    const files = Array.from(dt.files);
    if (files.length === 0) return;

    await this.processDroppedFiles(files);
  }

  private async processDroppedFiles(files: File[]) {
    // Validate files first
    const validFiles: File[] = [];
    const errors: string[] = [];

    for (const file of files) {
      const validation = this.validateFile(file);
      if (validation.valid) {
        validFiles.push(file);
      } else {
        errors.push(`${file.name}: ${validation.error}`);
      }
    }

    // Show validation errors if any
    if (errors.length > 0) {
      this.showValidationErrors(errors);
    }

    // Process valid files
    if (validFiles.length > 0) {
      await this.processBatchFiles(validFiles);
    }
  }

  private validateFile(file: File): { valid: boolean; error?: string } {
    // Check file size
    if (file.size > this.maxFileSize) {
      return {
        valid: false,
        error: `File too large (${this.formatFileSize(file.size)}). Maximum size is ${this.formatFileSize(this.maxFileSize)}`
      };
    }

    // Check file type
    const extension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!this.allowedTypes.includes(extension)) {
      return {
        valid: false,
        error: `Invalid file type. Only ${this.allowedTypes.join(', ')} files are supported`
      };
    }

    return { valid: true };
  }

  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  private showValidationErrors(errors: string[]) {
    const mainContentArea = document.getElementById('main-content-area');
    if (!mainContentArea) return;

    const errorHtml = `
      <div class="validation-errors" style="margin-bottom: 1.5rem;">
        <div style="background-color: var(--color-error); color: white; padding: 1rem; border-radius: var(--radius-md); margin-bottom: 1rem;">
          <h4 style="margin: 0 0 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"/>
              <line x1="15" y1="9" x2="9" y2="15"/>
              <line x1="9" y1="9" x2="15" y2="15"/>
            </svg>
            File Validation Errors
          </h4>
          <ul style="margin: 0; padding-left: 1.5rem;">
            ${errors.map(error => `<li>${error}</li>`).join('')}
          </ul>
        </div>
      </div>
    `;

    mainContentArea.insertAdjacentHTML('afterbegin', errorHtml);

    // Auto-remove errors after 10 seconds
    setTimeout(() => {
      const errorElement = mainContentArea.querySelector('.validation-errors');
      if (errorElement) {
        errorElement.remove();
      }
    }, 10000);
  }

  private async handleSelectFiles() {
    try {
      const selected = await open({
        multiple: true,
        filters: [
          {
            name: 'Email Files',
            extensions: ['eml', 'mbox']
          }
        ]
      });

      if (selected && Array.isArray(selected) && selected.length > 0) {
        await this.processSelectedFilePaths(selected);
      } else if (selected && typeof selected === 'string') {
        await this.processSelectedFilePaths([selected]);
      }
    } catch (error) {
      console.error('Error selecting files:', error);
      this.showError('Failed to select files. Please try again.');
    }
  }

  private async processSelectedFilePaths(filePaths: string[]) {
    // Convert file paths to file metadata for consistent processing
    const fileMetadata: FileMetadata[] = filePaths.map(path => ({
      name: path.split('\\').pop() || path.split('/').pop() || path,
      size: 0, // Size unknown for file paths
      type: path.split('.').pop()?.toLowerCase() || '',
      lastModified: Date.now(),
      path: path
    }));

    await this.processBatchFileMetadata(fileMetadata);
  }

  private async processBatchFiles(files: File[]) {
    // Convert File objects to FileMetadata for consistent processing
    const fileMetadata: FileMetadata[] = files.map(file => ({
      name: file.name,
      size: file.size,
      type: file.name.split('.').pop()?.toLowerCase() || '',
      lastModified: file.lastModified,
      path: file.path || file.name // Use file.path if available (Tauri), otherwise name
    }));

    await this.processBatchFileMetadata(fileMetadata);
  }

  private async processBatchFileMetadata(fileMetadata: FileMetadata[]) {
    const mainContentArea = document.getElementById('main-content-area');
    if (!mainContentArea) return;

    // Initialize processing states
    this.processingQueue = fileMetadata.map(file => ({
      file,
      status: 'pending',
      progress: 0,
      message: 'Waiting to process...'
    }));

    // Show batch processing UI
    this.renderBatchProcessingUI();

    // Process files sequentially to avoid overwhelming the backend
    for (let i = 0; i < this.processingQueue.length; i++) {
      await this.processQueuedFile(i);
    }
  }

  private renderBatchProcessingUI() {
    const mainContentArea = document.getElementById('main-content-area');
    if (!mainContentArea) return;

    const html = `
      <div class="batch-processing">
        <div class="section-header">
          <h3 class="section-title">Processing Email Files</h3>
          <p class="section-description">Processing ${this.processingQueue.length} file(s). Please wait...</p>
        </div>

        <div class="file-processing-list">
          ${this.processingQueue.map((item, index) => this.renderFileProcessingItem(item, index)).join('')}
        </div>

        <div class="processing-actions" style="margin-top: 1.5rem; display: flex; gap: 1rem;">
          <button id="cancel-processing-btn" class="btn btn-secondary">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"/>
              <line x1="15" y1="9" x2="9" y2="15"/>
              <line x1="9" y1="9" x2="15" y2="15"/>
            </svg>
            Cancel Processing
          </button>
          <button id="clear-completed-btn" class="btn btn-secondary" style="display: none;">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M3 6h18"/>
              <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
              <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
            </svg>
            Clear Completed
          </button>
        </div>
      </div>
    `;

    mainContentArea.innerHTML = html;

    // Add event listeners for processing actions
    const cancelBtn = document.getElementById('cancel-processing-btn');
    if (cancelBtn) {
      cancelBtn.addEventListener('click', () => this.cancelProcessing());
    }

    const clearBtn = document.getElementById('clear-completed-btn');
    if (clearBtn) {
      clearBtn.addEventListener('click', () => this.clearCompleted());
    }
  }

  private renderFileProcessingItem(item: ProcessingState, index: number): string {
    const statusIcon = this.getStatusIcon(item.status);
    const statusClass = this.getStatusClass(item.status);

    return `
      <div class="file-processing-item" data-index="${index}">
        <div class="file-header">
          <div class="file-info">
            <div class="file-name">${item.file.name}</div>
            <div class="file-details">
              ${item.file.size > 0 ? this.formatFileSize(item.file.size) : 'Unknown size'} •
              ${item.file.type.toUpperCase()} file
            </div>
          </div>
          <div class="file-status ${statusClass}">
            ${statusIcon}
            <span class="status-text">${item.message}</span>
          </div>
        </div>

        <div class="progress-container">
          <div class="progress-bar">
            <div class="progress-fill" style="width: ${item.progress}%; background-color: ${this.getProgressColor(item.status)}"></div>
          </div>
          <div class="progress-text">${item.progress}%</div>
        </div>

        ${item.result && item.status === 'completed' ? `
          <div class="processing-result">
            <div class="result-success">
              ✅ Successfully processed ${item.result.processed_count || 0} emails
            </div>
          </div>
        ` : ''}

        ${item.status === 'error' ? `
          <div class="processing-result">
            <div class="result-error">
              ❌ ${item.message}
              <button class="btn btn-sm btn-secondary retry-btn" data-index="${index}" style="margin-left: 1rem;">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="23 4 23 10 17 10"/>
                  <polyline points="1 20 1 14 7 14"/>
                  <path d="m3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"/>
                </svg>
                Retry
              </button>
            </div>
          </div>
        ` : ''}
      </div>
    `;
  }

  private getStatusIcon(status: ProcessingState['status']): string {
    switch (status) {
      case 'pending':
        return '⏳';
      case 'processing':
        return '🔄';
      case 'completed':
        return '✅';
      case 'error':
        return '❌';
      default:
        return '⏳';
    }
  }

  private getStatusClass(status: ProcessingState['status']): string {
    switch (status) {
      case 'pending':
        return 'status-pending';
      case 'processing':
        return 'status-processing';
      case 'completed':
        return 'status-completed';
      case 'error':
        return 'status-error';
      default:
        return 'status-pending';
    }
  }

  private getProgressColor(status: ProcessingState['status']): string {
    switch (status) {
      case 'completed':
        return 'var(--color-success)';
      case 'error':
        return 'var(--color-error)';
      case 'processing':
        return 'var(--color-primary)';
      default:
        return 'var(--color-gray-300)';
    }
  }

  private async processQueuedFile(index: number) {
    const item = this.processingQueue[index];
    if (!item) return;

    try {
      // Update status to processing
      item.status = 'processing';
      item.progress = 0;
      item.message = 'Processing...';
      this.updateFileProcessingUI(index);

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        if (item.status === 'processing' && item.progress < 90) {
          item.progress += Math.random() * 20;
          item.progress = Math.min(item.progress, 90);
          this.updateFileProcessingUI(index);
        }
      }, 500);

      const fileExtension = item.file.type || item.file.name.split('.').pop()?.toLowerCase();
      const fileType = fileExtension === 'mbox' ? 'mbox' : 'eml';

      const request: EmailProcessingRequest = {
        file_path: item.file.path || item.file.name,
        file_type: fileType
      };

      const response: EmailProcessingResponse = await invoke('process_email_file', { request });

      // Clear progress interval
      clearInterval(progressInterval);

      // Update final status
      if (response.success) {
        item.status = 'completed';
        item.progress = 100;
        item.message = `Completed successfully`;
        item.result = response;
      } else {
        item.status = 'error';
        item.progress = 100;
        item.message = response.message || 'Processing failed';
      }

    } catch (error) {
      console.error('Error processing file:', error);
      item.status = 'error';
      item.progress = 100;
      item.message = `Error: ${error}`;
    }

    this.updateFileProcessingUI(index);
    this.updateProcessingActions();
  }

  private updateFileProcessingUI(index: number) {
    const item = this.processingQueue[index];
    const itemElement = document.querySelector(`[data-index="${index}"]`);

    if (itemElement && item) {
      const newHtml = this.renderFileProcessingItem(item, index);
      itemElement.outerHTML = newHtml;

      // Re-attach retry button event listener if needed
      const retryBtn = document.querySelector(`[data-index="${index}"] .retry-btn`);
      if (retryBtn) {
        retryBtn.addEventListener('click', () => this.retryFile(index));
      }
    }
  }

  private updateProcessingActions() {
    const completedCount = this.processingQueue.filter(item => item.status === 'completed').length;
    const errorCount = this.processingQueue.filter(item => item.status === 'error').length;
    const totalCount = this.processingQueue.length;

    // Show clear completed button if there are completed items
    const clearBtn = document.getElementById('clear-completed-btn');
    if (clearBtn) {
      clearBtn.style.display = completedCount > 0 ? 'inline-flex' : 'none';
    }

    // Update cancel button text if all processing is done
    const cancelBtn = document.getElementById('cancel-processing-btn');
    if (cancelBtn && completedCount + errorCount === totalCount) {
      cancelBtn.textContent = 'Close';
      cancelBtn.onclick = () => this.showWelcomeMessage();
    }
  }

  private async retryFile(index: number) {
    const item = this.processingQueue[index];
    if (item) {
      item.status = 'pending';
      item.progress = 0;
      item.message = 'Retrying...';
      this.updateFileProcessingUI(index);
      await this.processQueuedFile(index);
    }
  }

  private cancelProcessing() {
    // Stop any ongoing processing
    this.processingQueue.forEach(item => {
      if (item.status === 'pending' || item.status === 'processing') {
        item.status = 'error';
        item.message = 'Cancelled by user';
      }
    });

    this.showWelcomeMessage();
  }

  private clearCompleted() {
    this.processingQueue = this.processingQueue.filter(item => item.status !== 'completed');

    if (this.processingQueue.length === 0) {
      this.showWelcomeMessage();
    } else {
      this.renderBatchProcessingUI();
    }
  }

  private showWelcomeMessage() {
    const mainContentArea = document.getElementById('main-content-area');
    if (mainContentArea) {
      mainContentArea.innerHTML = `
        <div class="welcome-message">
          <div style="text-align: center; padding: 3rem; color: var(--color-gray-500);">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" style="margin-bottom: 1rem; opacity: 0.5;">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
              <polyline points="22,6 12,13 2,6"/>
            </svg>
            <h3 style="margin-bottom: 0.5rem; color: var(--color-gray-700);">Welcome to AI Email Assistant</h3>
            <p>Select email files from the sidebar or drag and drop .eml/.mbox files here to begin processing.</p>
          </div>
        </div>
      `;
    }
  }



  private async checkServicesHealth() {
    const statusContainer = document.getElementById('service-status');
    if (!statusContainer) return;

    // Show checking status
    statusContainer.innerHTML = `
      <div class="status-item">
        <span class="status-indicator status-warning">
          <span class="status-dot"></span>
          Checking services...
        </span>
      </div>
    `;

    const services = [
      { name: 'Ingestion Service', url: 'http://localhost:8080' },
      { name: 'RAG Service', url: 'http://localhost:8000' },
      { name: 'Qdrant Database', url: 'http://localhost:6333' }
    ];

    const results: ServiceHealthResponse[] = [];

    for (const service of services) {
      try {
        const response: ServiceHealthResponse = await invoke('check_service_health', {
          serviceUrl: service.url
        });
        results.push({
          service: service.name,
          status: response.status,
          message: response.message
        });
      } catch (error) {
        results.push({
          service: service.name,
          status: 'error',
          message: `Connection failed: ${error}`
        });
      }
    }

    // Update UI with results
    this.updateServiceStatus(results);
  }

  private updateServiceStatus(results: ServiceHealthResponse[]) {
    const statusContainer = document.getElementById('service-status');
    if (!statusContainer) return;

    statusContainer.innerHTML = results.map(result => {
      const statusClass = result.status === 'healthy' ? 'status-healthy' :
                         result.status === 'error' ? 'status-error' : 'status-warning';

      return `
        <div class="status-item" style="margin-bottom: 0.5rem;">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span style="font-weight: 500; font-size: var(--font-size-sm);">${result.service}</span>
            <span class="status-indicator ${statusClass}">
              <span class="status-dot"></span>
              ${result.status}
            </span>
          </div>
          ${result.message ? `<p style="font-size: var(--font-size-xs); color: var(--color-gray-500); margin-top: 0.25rem;">${result.message}</p>` : ''}
        </div>
      `;
    }).join('');
  }

  private showError(message: string) {
    const mainContentArea = document.getElementById('main-content-area');
    if (mainContentArea) {
      mainContentArea.innerHTML = `
        <div style="text-align: center; padding: 3rem; color: var(--color-error);">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" style="margin-bottom: 1rem;">
            <circle cx="12" cy="12" r="10"/>
            <line x1="15" y1="9" x2="9" y2="15"/>
            <line x1="9" y1="9" x2="15" y2="15"/>
          </svg>
          <h3 style="margin-bottom: 0.5rem;">Error</h3>
          <p>${message}</p>
        </div>
      `;
    }
  }
}

// Initialize the application
new EmailAssistantApp();
