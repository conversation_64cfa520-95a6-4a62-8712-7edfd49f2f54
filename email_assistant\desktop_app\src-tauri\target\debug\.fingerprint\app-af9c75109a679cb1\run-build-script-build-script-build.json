{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 10451745322295484367], [14039947826026167952, "build_script_build", false, 9844267916455972672], [14525517306681678134, "build_script_build", false, 17055964569888588626], [6416823254013318197, "build_script_build", false, 9931762977411470621], [16171925541490437305, "build_script_build", false, 7816199916948509451], [8324462083842905811, "build_script_build", false, 4646339959944027737]], "local": [{"RerunIfChanged": {"output": "debug\\build\\app-af9c75109a679cb1\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}